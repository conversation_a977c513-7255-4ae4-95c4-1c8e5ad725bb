package com.ecco.serviceConfig.dom;

import com.ecco.infrastructure.dom.ConfigCommand;
import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("taskDef")
public class TaskDefinitionCommand extends ConfigCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public TaskDefinitionCommand() {
        super();
    }

    public TaskDefinitionCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                 long userId, @Nonnull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

}
