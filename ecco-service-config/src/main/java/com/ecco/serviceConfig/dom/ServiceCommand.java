package com.ecco.serviceConfig.dom;

import com.ecco.infrastructure.dom.ConfigCommand;

import java.util.UUID;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import org.joda.time.Instant;

@Entity
@DiscriminatorValue("service")
public class ServiceCommand extends ConfigCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ServiceCommand() {
        super();
    }

    public ServiceCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
            long userId, @Nonnull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

}
