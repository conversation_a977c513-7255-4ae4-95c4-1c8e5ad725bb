package com.ecco.serviceConfig.repositories;

import java.util.stream.Stream;
import javax.annotation.Nonnull;
import javax.persistence.QueryHint;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import org.springframework.data.jpa.repository.QueryHints;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface ConfigCommandRepository extends BaseCommandRepository<ConfigCommand, Long> {

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Nonnull
    Stream<ConfigCommand> findAll();
}
