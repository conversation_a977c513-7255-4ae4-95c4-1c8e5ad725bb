package com.ecco.acceptancetests.api.evidence;

import com.ecco.acceptancetests.Constants;
import com.ecco.data.client.ServiceOptions;
import com.ecco.data.client.WebApiSettings;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dom.upload.UploadedFile;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.webApi.evidence.*;
import com.ecco.upload.webapi.UploadedFileResource;
import org.joda.time.Instant;
import org.joda.time.LocalDateTime;
import org.junit.Before;
import org.junit.Test;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.joda.time.DateTimeZone.UTC;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public abstract class BaseEvidenceCommandAPITests<WORK_VM extends BaseWorkViewModel> extends BaseReferralCommandAPITests {

    protected EvidenceGroup evidenceGroup;
    protected EvidenceTask defaultEvidenceTask;
    protected UUID workUuid;
    /** populated with a date up to 50 days in past at beginning of each test */
    protected LocalDateTime workDateUpTo50DaysInPast;

    @Before
    public void createDefinitionData() {
        workUuid = UUID.randomUUID(); // should be diff work uuid per test
        workDateUpTo50DaysInPast = LocalDateTime.now().minusDays(random.nextInt(50));
        ensureDefinitionIds();
    }

    /** must set actionDefId to one relevant for the test - differs between threat and needs */
    abstract protected void ensureDefinitionIds();

    public BaseEvidenceCommandAPITests(EvidenceGroup evidenceGroup, EvidenceTask defaultEvidenceTask, ServiceOptions service) {
        super(service);
        this.evidenceGroup = evidenceGroup;
        this.defaultEvidenceTask = defaultEvidenceTask;
    }

    /** Needs implementing to get the work from the correct API */
    protected abstract List<WORK_VM> findWorkSummaryByServiceRecipientId();

    protected WORK_VM findWorkByUuid(UUID uuid) {
        //noinspection OptionalGetWithoutIsPresent
        return findOptionalWorkByUuid(uuid).get();
    }

    protected EvidenceSmartStepViewModel findWorkActionInstanceUuid(UUID workUuid, UUID uuid) {
        EvidenceSupportWorkViewModel lastEvidence = (EvidenceSupportWorkViewModel) findWorkByUuid(workUuid);
        return findWorkActionInstanceUuid(lastEvidence, uuid);
    }

    protected EvidenceSmartStepViewModel findWorkActionInstanceUuid(EvidenceSupportWorkViewModel evidence, UUID uuid) {
        assertNotNull(evidence);
        EvidenceSmartStepViewModel actionVm = evidence.actions.stream()
                .filter(a -> a.actionInstanceUuid.equals(uuid))
                .findFirst()
                .orElseThrow();
        assertEquals(uuid, actionVm.actionInstanceUuid);
        return actionVm;
    }

    protected EvidenceThreatActionViewModel findWorkRiskActionInstanceUuid(EvidenceThreatWorkViewModel evidence, UUID uuid) {
        assertNotNull(evidence);
        EvidenceThreatActionViewModel actionVm = evidence.riskActions.stream()
                .filter(a -> a.actionInstanceUuid.equals(uuid))
                .findFirst()
                .orElseThrow();
        assertEquals(uuid, actionVm.actionInstanceUuid);
        return actionVm;
    }

    protected Optional<WORK_VM> findOptionalWorkByUuid(UUID uuid) {
        List<WORK_VM> work = findWorkSummaryByServiceRecipientId();
        return work.stream()
                .filter(input -> uuid.equals(input.id))
                .findFirst();
    }

    protected CommentCommandViewModel sendCommentCommand(String commentToSave, LocalDateTime workDate) {
        return sendCommentCommand(workUuid, commentToSave, null, workDate);
    }

    /** Create a comment for a new work item using the same referral created in the base class */
    protected CommentCommandViewModel sendCommentCommand(UUID workUuid, String commentToSave, LocalDateTime workDate) {
        CommentCommandViewModel ccvm = createCommentCommand(workUuid, commentToSave, null, workDate);
        commandActor.executeCommand(ccvm);
        return ccvm;
    }
    protected CommentCommandViewModel sendCommentCommand(UUID workUuid, String commentToSave,
                                                         @Nullable Instant createdCommand, LocalDateTime workDate) {
        CommentCommandViewModel ccvm = createCommentCommand(workUuid, commentToSave, createdCommand, workDate);
        commandActor.executeCommand(ccvm);
        return ccvm;
    }

    protected CommentCommandViewModel createCommentCommand(String commentToSave, LocalDateTime workDate) {
        return createCommentCommand(workUuid, commentToSave, null, workDate);
    }

    protected CommentCommandViewModel createCommentCommand(UUID workUuid, String commentToSave,
                                                           Instant createdCommand, LocalDateTime workDate) {
        CommentCommandViewModel ccvm = createCommentCommandViewModel(workUuid, rvm.serviceRecipientId,
                evidenceGroup, defaultEvidenceTask, commentToSave, createdCommand, workDate, 10 + new Random().nextInt(145));
        return ccvm;
    }

    protected CommentCommandViewModel createCommentCommandViewModel(UUID workUuid, int serviceRecipientId, EvidenceGroup group,
                                                                    EvidenceTask sourceTask, String comment,
                                                                    LocalDateTime workDate, Integer minsSpent) {
        return createCommentCommandViewModel(workUuid, serviceRecipientId, group, sourceTask, comment, null, workDate, minsSpent);
    }

    protected CommentCommandViewModel createCommentCommandViewModel(UUID workUuid, int serviceRecipientId, EvidenceGroup group,
            EvidenceTask sourceTask, String comment,
            Instant createdCommand, LocalDateTime workDate, Integer minsSpent) {
        CommentCommandViewModel cc = new CommentCommandViewModel(workUuid, serviceRecipientId, group, sourceTask);
        if (createdCommand != null) {
            cc.timestamp = createdCommand;
        }
        if (workDate != null) {
            cc.workDate = ChangeViewModel.create(null, workDate);
        }
        if (comment != null) {
            cc.comment = ChangeViewModel.create(null, comment);
        }
        if (minsSpent != null) {
            cc.minsSpent = ChangeViewModel.create(null, minsSpent);
        }
        return cc;
    }

    private Stream<CommentCommandViewModel> fetchCommentCommands(UUID uuid) {
        List<CommentCommandViewModel> ccvms = commentCommandActor.findCommentCommands(rvm.serviceRecipientId, evidenceGroup).getBody();
        return ccvms.stream().filter(input -> uuid.equals(input.uuid));
    }

    /** Ensures that the comment and workDate that we provided is correct after all commands are applied */
    void verifyThatEvidenceMatchesCommentCommand(CommentCommandViewModel commentCommand,
            BaseWorkViewModel lastEvidence) {
        assertThat(lastEvidence.workDate).isEqualTo(commentCommand.workDate.to.withMillisOfSecond(0));
        assertThat(lastEvidence.comment).is(resultOfChange(commentCommand.comment));
    }

    void verifyNoCommentAndDefaultWorkDate(BaseWorkViewModel lastEvidence, Instant timestamp) {
        assertThat(lastEvidence.comment).isEqualTo(null);
        assertThat(lastEvidence.workDate).isEqualTo(timestamp.toDateTime(UTC).toLocalDateTime().withMillisOfSecond(0));
    }

    @Test
    public void commentCommandShouldCreateAWorkItemWithMatchingWorkDate() {
        CommentCommandViewModel ccvm = sendCommentCommand("parents are amazing", workDateUpTo50DaysInPast);
        BaseWorkViewModel evidence = findWorkByUuid(ccvm.workUuid);
        verifyThatEvidenceMatchesCommentCommand(ccvm, evidence);
    }

    @Test
    public void commentCommandSaved() {
        CommentCommandViewModel ccvm = sendCommentCommand("I want to build the world a home", workDateUpTo50DaysInPast);

        Stream<CommentCommandViewModel> filtered = fetchCommentCommands(ccvm.uuid);
        assertThat(filtered).hasAtLeastOneElementOfType(CommentCommandViewModel.class);
    }

    /**
     * Create a file on the server using standard GET/POST requests.
     * Liquibase could have been an idea, but we can't guarantee the server is in 'test-date' mode.
     */
    protected long standardAttachment(String evidenceGroupName) {
        String jsessionid = userActor.formLogin(Constants.SYSADMIN_USERNAME, Constants.SYSADMIN_PASSWORD);

        String urlController = WebApiSettings.APPLICATION_URL + "/api/secure/uploadHiddenJs.html";
        String uri = UriComponentsBuilder.fromHttpUrl(urlController)
                .queryParam("source", UploadedFile.Source.SERVICE_RECIPIENT)
                .queryParam("evidenceGroupName", evidenceGroupName)
                .queryParam("serviceRecipientId", rvm.serviceRecipientId)
                .toUriString();

        UploadedFileResource result = userActor.nonWebapiFileUpload(jsessionid, uri);
        return result.fileId;
    }

}