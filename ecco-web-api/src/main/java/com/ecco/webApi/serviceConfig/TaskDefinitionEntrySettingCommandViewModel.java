package com.ecco.webApi.serviceConfig;

import com.ecco.dto.ChangeViewModel;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

import org.springframework.web.util.UriComponentsBuilder;

public class TaskDefinitionEntrySettingCommandViewModel extends ServiceTypeCommandViewModel {

    @Nonnull
    public String taskName;
    @Nonnull
    public String settingName;
    @Nullable
    public ChangeViewModel<String> valueChange;

    /** For Jackson etc */
    @Deprecated
    TaskDefinitionEntrySettingCommandViewModel() {
        super();
    }

    public TaskDefinitionEntrySettingCommandViewModel(long serviceTypeId, @Nonnull String taskName, @Nonnull String settingName) {
        super(UriComponentsBuilder
                .fromUriString("service-config/{serviceTypeId}/task/{taskName}/setting/{settingName}/")
                .buildAndExpand(serviceTypeId, taskName, settingName).toUriString(), serviceTypeId);
        this.taskName = taskName;
        this.settingName = settingName;
    }

    public boolean hasChanges() {
        return valueChange != null;
    }

}