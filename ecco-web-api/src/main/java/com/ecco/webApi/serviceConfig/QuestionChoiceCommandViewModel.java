package com.ecco.webApi.serviceConfig;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

/**
 * Configuration of a question answer choice
 */
@Slf4j
public class QuestionChoiceCommandViewModel extends QuestionnaireBaseCommandViewModel {

    @Nullable
    public ChangeViewModel<String> nameChange;

    @Nullable
    public ChangeViewModel<String> valueChange; // 'add' only for now, and probably forever

    @Nullable
    public ChangeViewModel<Boolean> disableChange;

    @Nullable
    public Integer questionId;

    /** only for Cglib/Hibernate etc */
    @Deprecated
    protected QuestionChoiceCommandViewModel() {
        super();
    }

    public boolean hasChanges() {
        return nameChange != null || valueChange != null || disableChange != null;
    }

    public QuestionChoiceCommandViewModel(@Nonnull String operation, Integer questionId, Integer id) {
        super(UriComponentsBuilder
                .fromUriString("config/questionnaire/questionChoice/")
                .toUriString(), operation, id);
        this.questionId = questionId;
    }

    // TODO toString inc all inherited fields

    public boolean valid() {

        boolean valid = BaseCommandViewModel.valid(getClass(), this);

        if (StringUtils.equalsIgnoreCase(this.operation, BaseCommandViewModel.OPERATION_ADD)) {
            if (this.questionId == null) {
                log.error("Required field: questionId");
                return false;
            }
        }

        return valid;
    }

}
