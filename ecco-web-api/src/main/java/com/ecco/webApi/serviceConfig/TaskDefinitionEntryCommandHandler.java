package com.ecco.webApi.serviceConfig;

import javax.annotation.Nonnull;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ecco.serviceConfig.dom.ServiceType_TaskDefinition;
import com.ecco.serviceConfig.dom.ServiceType_TaskDefinition_MultiId;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.webApi.CommandResult;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.ecco.infrastructure.dom.ConfigCommand;
import com.ecco.serviceConfig.dom.TaskDefinitionEntryCommand;
import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.serviceConfig.repositories.TaskDefinitionRepository;
import com.ecco.serviceConfig.repositories.TaskDefinitionEntryRepository;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.Serializable;

@Component
public class TaskDefinitionEntryCommandHandler extends BaseCommandHandler<TaskDefinitionEntryCommandViewModel, Long,
            ConfigCommand, TaskDefinitionEntryParams> {

    @PersistenceContext
    private EntityManager entityManager;

    @Nonnull private final TaskDefinitionEntryRepository taskDefinitionEntryRepository;
    @Nonnull private final TaskDefinitionRepository taskDefinitionRepository;
    @Nonnull private final ServiceTypeService serviceTypeService;

    @Autowired
    public TaskDefinitionEntryCommandHandler(ObjectMapper objectMapper,
            TaskDefinitionEntryRepository taskDefinitionEntryRepository, ConfigCommandRepository configCommandRepository,
            TaskDefinitionRepository taskDefinitionRepository,
            ServiceTypeService serviceTypeService) {
        super(objectMapper, configCommandRepository, TaskDefinitionEntryCommandViewModel.class);
        this.taskDefinitionEntryRepository = taskDefinitionEntryRepository;
        this.taskDefinitionRepository = taskDefinitionRepository;
        this.serviceTypeService = serviceTypeService;
    }

    @Override
    protected CommandResult handleInternal(@NotNull Authentication auth, TaskDefinitionEntryParams params, @NotNull TaskDefinitionEntryCommandViewModel viewModel) {
        long taskDefinitionId = this.taskDefinitionRepository.findOneByNameIgnoreCase(params.taskName).getId();

        switch (viewModel.operation) {
            case TaskDefinitionEntryCommandViewModel.OPERATION_ADD:
                this.addTaskEntryDefinition(auth, params, viewModel, taskDefinitionId);
                break;

            case TaskDefinitionEntryCommandViewModel.OPERATION_UPDATE:
                this.updateTaskEntryDefinition(auth, params, viewModel, taskDefinitionId);
                break;

            case TaskDefinitionEntryCommandViewModel.OPERATION_REMOVE:
                this.removeTaskEntryDefinition(auth, params, viewModel, taskDefinitionId);
                break;

            default:
                throw new IllegalArgumentException("invalid operation: " + viewModel.operation);
        }
        return null; // TODO: Could return Hateoas Link to created/updated
    }

    private void addTaskEntryDefinition(Authentication auth, TaskDefinitionEntryParams params,
            TaskDefinitionEntryCommandViewModel cmdVM, long taskDefinitionId) {
        ServiceType_TaskDefinition taskDefinitionEntry = ServiceType_TaskDefinition.create(entityManager, params.serviceTypeId, taskDefinitionId);
        this.applyChanges(taskDefinitionEntry, cmdVM);
        this.taskDefinitionEntryRepository.save(taskDefinitionEntry);
        serviceTypeService.evictOneDtoInCache(Math.toIntExact(cmdVM.serviceTypeId));
    }

    private void updateTaskEntryDefinition(Authentication auth, TaskDefinitionEntryParams params,
            TaskDefinitionEntryCommandViewModel cmdVM, long taskDefinitionId) {
        ServiceType_TaskDefinition_MultiId multiId = ServiceType_TaskDefinition_MultiId.create(entityManager, params.serviceTypeId, taskDefinitionId);
        ServiceType_TaskDefinition taskDefinitionEntry = this.taskDefinitionEntryRepository.findById(multiId).orElseThrow(NullPointerException::new);
        this.applyChanges(taskDefinitionEntry, cmdVM);
        this.taskDefinitionEntryRepository.save(taskDefinitionEntry);
        serviceTypeService.evictOneDtoInCache(Math.toIntExact(cmdVM.serviceTypeId));
    }

    private void removeTaskEntryDefinition(Authentication auth, TaskDefinitionEntryParams params,
            TaskDefinitionEntryCommandViewModel cmdVM, long taskDefinitionId) {
        ServiceType_TaskDefinition_MultiId multiId = ServiceType_TaskDefinition_MultiId.create(entityManager, params.serviceTypeId, taskDefinitionId);
        ServiceType_TaskDefinition taskDefinitionEntry = this.taskDefinitionEntryRepository.findById(multiId).orElseThrow(NullPointerException::new);
        // TODO: work out why this JPA-QL delete works and the JPA repository one below doesn't (something to do with @EmbeddedId, would be my guess).
        //         this.taskDefinitionEntryRepository.delete(taskDefinitionEntry);
        entityManager.createQuery("DELETE FROM ServiceType_TaskDefinitionSetting WHERE serviceType_TaskDefinition.multiId.serviceType = " + params.serviceTypeId + " AND serviceType_TaskDefinition.multiId.taskDefinition = " + taskDefinitionId).executeUpdate();
        int c = entityManager.createQuery("DELETE FROM ServiceType_TaskDefinition WHERE multiId.serviceType = " + params.serviceTypeId + " AND multiId.taskDefinition = " + taskDefinitionId).executeUpdate();
        Assert.isTrue(c > 0);
        entityManager.flush();
        serviceTypeService.evictOneDtoInCache(Math.toIntExact(cmdVM.serviceTypeId));
    }

    private void applyChanges(ServiceType_TaskDefinition taskDefinitionEntry, TaskDefinitionEntryCommandViewModel cmdVM) {
        if (cmdVM.hasChanges()) {
            if (cmdVM.allowNextChange != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.allowNextChange, taskDefinitionEntry.isAllowNext(), "allowNext");
                taskDefinitionEntry.setAllowNext(cmdVM.allowNextChange.to);
            }
            if (cmdVM.orderbyChange != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.orderbyChange, taskDefinitionEntry.getOrderby(), "orderBy");
                taskDefinitionEntry.setOrderby(cmdVM.orderbyChange.to);
            }
            if (cmdVM.dueDateScheduleChange != null) {
                warnIfPrevValueDoesntMatch(cmdVM, cmdVM.dueDateScheduleChange, taskDefinitionEntry.getDueDateSchedule(), "dueDateSchedule");
                taskDefinitionEntry.setDueDateSchedule(cmdVM.dueDateScheduleChange.to);
            }
        }
    }

    @Override
    protected ConfigCommand createCommand(Serializable targetId, TaskDefinitionEntryParams params, String requestBody,
                                          TaskDefinitionEntryCommandViewModel viewModel, long userId) {
        Assert.state(params.serviceTypeId == viewModel.serviceTypeId, "serviceTypeId in body must match URI");
        Assert.state(params.taskName.equals(viewModel.taskName), "taskName in body must match URI");

        return new TaskDefinitionEntryCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody);
    }

}
