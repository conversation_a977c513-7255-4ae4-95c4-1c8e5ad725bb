package com.ecco.webApi.calendar;

import com.ecco.calendar.core.CalendarException;
import com.ecco.calendar.core.CalendarService;
import com.ecco.calendar.core.Recurrence.RecurrenceHandle;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionTemplate;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.List;

/**
 * This was used to fixup DEV-1030 which allowed evidence to be saved to events
 * which weren't concrete recurrences. The evidence saved against <uuid>:<datetime>
 * but the cosmo_item entry was never made.
 */
@Slf4j
@RequiredArgsConstructor
public class EnsureConcreteRecurrences implements SmartInitializingSingleton {

    @PersistenceContext
    private EntityManager entityManager;

    private final CalendarService calendarService;
    private final PlatformTransactionManager txManager;

    @Override
    public void afterSingletonsInstantiated() {
        log.info("Migrate: Ensuring concrete recurrences..");
        new TransactionTemplate(txManager).<Void>execute(this::doInTransaction);
        log.info("Migrate: Ensuring concrete recurrences.. COMPLETE");
    }

    private Void doInTransaction(TransactionStatus transactionStatus) {
        List results = entityManager
                .createNativeQuery("select w.eventId from supportplanwork w left join cosmo_item ci on w.eventId=ci.item_uid where w.eventId like '%:%' and ci.item_uid is null")
                .getResultList();
        log.info("Migrate: Ensuring concrete recurrences.. found {} items to make concrete", results.size());
        results.forEach(r -> this.createConcreteEntry((String) r));
        return null;
    }

    private void createConcreteEntry(String uid) {
        if (!calendarService.isRecurrence(uid)) {
            log.warn("Migrate: Ensuring concrete recurrences.. NOT RECURRING (no action) {}", uid);
            return;
        }
        // get the non-recurring uid
        RecurringEntryHandle handle = calendarService.getEntryHandleFromRecurrenceHandle(RecurrenceHandle.fromString(uid));
        try {
            calendarService.findEntry(handle.toString());
        } catch (CalendarException e) {
            log.warn("Migrate: Ensuring concrete recurrences.. MISSING master for {}", uid);
            return;
        }
        log.info("Migrate: Ensuring concrete recurrences.. making concrete {}", uid);

        calendarService.ensureConcreteRecurrence(RecurrenceHandle.fromString(uid), null);
    }

}
