package com.ecco.webApi.evidence;

import com.ecco.dao.EvidenceQuestionAnswerRepository;
import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.dom.EvidenceSupportAnswer;
import com.ecco.dom.EvidenceSupportWork;
import com.ecco.dom.commands.QuestionAnswerCommand;
import com.ecco.evidence.ParentChildResolver;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.service.TaskDefinitionService;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.CommandResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.calendar.core.CalendarService;
import org.joda.time.DateTimeZone;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.io.Serializable;
import java.util.List;

import static com.ecco.security.SecurityUtil.getUser;

public class QuestionAnswerCommandHandler extends EvidenceCommandHandler<QuestionAnswerCommandViewModel, QuestionAnswerCommand, QuestionAnswerParams> {

    private static final Logger log = LoggerFactory.getLogger(QuestionAnswerCommandHandler.class);

    @Nonnull private final EvidenceQuestionAnswerRepository questionAnswerRepository;

    @Nonnull private final EvidenceSupportWorkRepository workRepository;

    public QuestionAnswerCommandHandler(ObjectMapper objectMapper,
                                        ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                        EvidenceSupportWorkRepository workRepository,
                                        EvidenceQuestionAnswerRepository questionAnswerRepository,
                                        ServiceRepository serviceRepository,
                                        ServiceRecipientRepository serviceRecipientRepository,
                                        ParentChildResolver parentChildResolver,
                                        @Nonnull EntityUriMapper entityUriMapper,
                                        @Nonnull CalendarService calendarService,
                                        TaskDefinitionService taskDefinitionService) {
        super(objectMapper, serviceRecipientRepository, serviceRecipientCommandRepository,
                serviceRepository, parentChildResolver, calendarService, taskDefinitionService, entityUriMapper,
                QuestionAnswerCommandViewModel.class);
        this.workRepository = workRepository;
        this.questionAnswerRepository = questionAnswerRepository;
    }

    @Override
    public CommandResult handleInternal(int parentServiceRecipientId, Integer childServiceRecipientId,
                                        Authentication auth, QuestionAnswerParams params, QuestionAnswerCommandViewModel viewModel) {

        // don't re-check the EvidenceTask since we've just done that in the QuestionAnswerCommandController

        if (!viewModel.hasChanges()) {
            log.debug("viewModel has no changes: ", viewModel);
            return null;
        }

        var grp = taskDefinitionService.findGroupFromGroupName(params.evidenceGroupKey);

        // see what the last answer was at the time we made the change
        List<EvidenceSupportAnswer> snapshot = questionAnswerRepository
                .findLatestByServiceRecipientIdAndEvidenceGroupIdAndQuestionIdAndCreatedLessOrEqualTo(
                        parentServiceRecipientId, viewModel.questionDefId,
                        grp.getId(),
                        viewModel.timestamp.toDateTime(DateTimeZone.UTC), PageRequest.of(0, 1));

        EvidenceSupportAnswer newSnapshot, previousSnapshot;
        if (snapshot.isEmpty()) {
            newSnapshot = EvidenceSupportAnswer.builder(
                    parentServiceRecipientId,
                    params.questionDefId).build();
            previousSnapshot = newSnapshot;
        }
        else {
            previousSnapshot = snapshot.get(0);
            newSnapshot = EvidenceSupportAnswer.fromPrevious(previousSnapshot);
        }

        // findOrCreateWork - for questionanswers we can assume a work is already saved
        // being the first in the queue from the client (we couldn't assume this
        // on the goals because of the new visual suppport work starting with goals)
        EvidenceSupportWork work = workRepository.findById(viewModel.workUuid).orElse(null);
        newSnapshot.setWork(work);

        applyCommonUpdates(auth, viewModel, previousSnapshot, newSnapshot);

        questionAnswerRepository.save(newSnapshot);
        return null;
    }

    @Override
    protected QuestionAnswerCommand createCommand(Serializable targetId, QuestionAnswerParams params, String requestBody,
                                                  QuestionAnswerCommandViewModel viewModel, long userId) {
        Assert.state(params.serviceRecipientId == viewModel.serviceRecipientId, "serviceRecipientId in body must match URI");
        Assert.state(params.questionDefId == viewModel.questionDefId, "questionId in body must match URI");

        return new QuestionAnswerCommand(viewModel.uuid, viewModel.timestamp, userId, requestBody, params.serviceRecipientId,
                params.questionDefId, params.evidenceGroupKey, params.taskName);
    }

    private void applyCommonUpdates(Authentication authentication,
            QuestionAnswerCommandViewModel vm, EvidenceSupportAnswer previousSnapshot,
            EvidenceSupportAnswer newSnapshot) {
        Assert.notNull(previousSnapshot);
        Assert.notNull(newSnapshot.getWork());

        newSnapshot.setCreated(vm.timestamp.toDateTime(DateTimeZone.UTC));

        newSnapshot.setAnswer(vm.answerChange != null ?
                            vm.answerChange.to : null);

        // Copy work date to answer from GenericTypeWork
        newSnapshot.setWorkDate(newSnapshot.getWork().getWorkDate());

        newSnapshot.setAuthor(getUser(authentication).getContact());
    }

}
