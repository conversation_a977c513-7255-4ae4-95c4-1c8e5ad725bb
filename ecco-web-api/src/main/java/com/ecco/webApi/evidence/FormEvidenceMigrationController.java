package com.ecco.webApi.evidence;

import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * A temporary class to allow is to test the migration without restarting.
 * TODO Remove from 21.05. This currently isn't used, and will break with a null formDefinitionUuid argument.
 */
@Secured("ROLE_SYSADMIN")
@RestController
@RequiredArgsConstructor
public class FormEvidenceMigrationController extends BaseWebApiController {

    private final FormEvidenceMigration formEvidenceMigration;

    @PostJson("/tmp/migration/referral/{referralId}/evidence/{evidenceGroup}/form/flagMap/")
    public Result patchSnapshot(@PathVariable long referralId, @RequestBody String flagMapAsCSV, @PathVariable String evidenceGroup) {
        String id = formEvidenceMigration.singleReferralFromFlagMap(referralId, flagMapAsCSV, new HashMap<>(), evidenceGroup, null);
        return id == null ? new Result("command not required, or error", "") : new Result(Result.COMMAND_APPLIED, id);
    }

}
