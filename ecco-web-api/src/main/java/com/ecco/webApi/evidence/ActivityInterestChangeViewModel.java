package com.ecco.webApi.evidence;

import javax.annotation.Nonnull;

/**
 * Data-transfer object representing a command to add or remove an association
 * between a service recipient and an Activity Type.
 */
public class ActivityInterestChangeViewModel extends BaseCommandViewModel {
    @Nonnull
    public static final String OPERATION_ADD = "add";

    @Nonnull
    public static final String OPERATION_REMOVE = "remove";

    /**
     * The operation to perform; either {@link #OPERATION_ADD}, or
     * {@link #OPERATION_REMOVE}.
     */
    @Nonnull
    public String operation;

    /**
     * The ID of the Activity Type to be added or removed.
     */
    public int activityTypeId;
}
