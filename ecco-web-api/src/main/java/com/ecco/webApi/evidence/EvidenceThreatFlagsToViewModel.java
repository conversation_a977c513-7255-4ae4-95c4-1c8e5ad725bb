package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceThreatFlag;
import com.ecco.webApi.controllers.EvidenceThreatWorkToViewModel;

import javax.annotation.Nullable;
import java.util.function.Function;

/**
 * Representation of a flag across a collection of flags spanning work and srIds
 */
public class EvidenceThreatFlagsToViewModel implements Function<EvidenceThreatFlag, EvidenceThreatFlagsViewModel> {

    EvidenceThreatWorkToViewModel threatWorkToViewModel = new EvidenceThreatWorkToViewModel(true);

    @Override
    @Nullable
    public EvidenceThreatFlagsViewModel apply(@Nullable EvidenceThreatFlag input) {
        if (input == null) {
            throw new NullPointerException("input GenericTypeFlag must not be null");
        }

        var viewModel = new EvidenceThreatFlagsViewModel();
        EvidenceBaseFlagsToViewModel.apply(viewModel, input);
        viewModel.work = threatWorkToViewModel.apply(input.getWork());
        return viewModel;
    }

}
