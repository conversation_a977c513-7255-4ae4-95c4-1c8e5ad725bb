package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceGroup;
import com.ecco.evidence.EvidenceTask;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.UUID;

/**
 * A contact associated with a piece of evidence.
 * This can be used to record lone working information on the worker.
 */
public class EvidenceAssociatedContactCommandViewModel extends BaseServiceRecipientCommandViewModel {

    /** For Jackson etc */
    EvidenceAssociatedContactCommandViewModel() {
        super();
    }

    public enum AttendanceStatus {
        START,
        END
    }

    @Nonnull
    public UUID workUuid;

    public long contactId;

    // Non-null because we don't have a snapshot to update yet
    @Nonnull
    public AttendanceStatus attendanceStatus;

    /**
     * The eventId uid that the work is for (eg 'rota visit' calendar uid)
     */
    @Nonnull
    public String eventId;

    /**
     * The location of the contact at this (created) moment in time.
     * {@link CommentCommandViewModel}
     */
    @Nullable
    public LocationViewModel location;

    public EvidenceAssociatedContactCommandViewModel(@Nonnull UUID workUuid,
                                                     @Nonnull String eventId,
                                                     int serviceRecipientId,
                                                     @Nonnull EvidenceGroup evidenceGroup,
                                                     @Nonnull EvidenceTask evidenceTask,
                                                     long contactId) {
        super(UriComponentsBuilder
            .fromUriString("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/{sourceTaskName}/contact/{contactId}/")
            .buildAndExpand(serviceRecipientId, evidenceGroup.nameAsLowercase(), evidenceTask.getTaskName(), contactId)
            .toString(), serviceRecipientId);
        this.contactId = contactId;
        this.eventId = eventId;
        this.workUuid = workUuid;
    }

    public boolean hasChanges() {
        return true;
    }

}
