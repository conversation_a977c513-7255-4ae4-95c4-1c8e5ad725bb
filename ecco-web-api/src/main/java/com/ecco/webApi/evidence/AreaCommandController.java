package com.ecco.webApi.evidence;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.ecco.evidence.EvidenceTask;
import com.ecco.service.TaskDefinitionService;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;

import java.io.IOException;
import javax.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AreaCommandController extends BaseWebApiController {

    @Nonnull
    private final RiskAreaCommandHandler areaCommandHandler;

    @Nonnull
    private final TaskDefinitionService taskDefinitionService;

    @Autowired
    public AreaCommandController(@Nonnull RiskAreaCommandHandler areaCommandHandler,
                                 @Nonnull TaskDefinitionService taskDefinitionService) {
        this.areaCommandHandler = areaCommandHandler;
        this.taskDefinitionService = taskDefinitionService;
    }

    @RequestMapping(value = "/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupKey}/{taskName}/area/{areaDefId}/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result updateArea(
            AreaParams params,
            @Nonnull Authentication authentication,
            @Nonnull @RequestBody String requestBody) throws IOException {

        EvidenceTask task = EvidenceTask.fromTaskName(params.taskName);

        var type = taskDefinitionService.getTaskType(task);
        if (taskDefinitionService.isThreatBased(type)) {
            return areaCommandHandler.handleCommand(authentication, params, requestBody);
        } else {
            throw new IllegalArgumentException("Unsupported evidenceGroupKey: " + params.evidenceGroupKey);
        }
    }
}
