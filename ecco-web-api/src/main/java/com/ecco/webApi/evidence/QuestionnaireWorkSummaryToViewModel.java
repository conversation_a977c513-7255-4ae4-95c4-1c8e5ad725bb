package com.ecco.webApi.evidence;

import com.ecco.dao.QuestionnaireWorkSummary;
import com.ecco.webApi.upload.UploadedFileResourceAssembler;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

public class QuestionnaireWorkSummaryToViewModel implements Function<QuestionnaireWorkSummary, QuestionnaireEvidenceViewModel> {

    private final AnswerSummaryToSnapshotViewModel answersToSnapshotViewModel = new AnswerSummaryToSnapshotViewModel();
    private final UploadedFileResourceAssembler fileAssembler;
    private final EvidenceFlagSummaryToViewModel flagsToViewModel = new EvidenceFlagSummaryToViewModel();

    public QuestionnaireWorkSummaryToViewModel(UploadedFileResourceAssembler assembler) {
        this.fileAssembler = assembler;
    }

    @Override
    @Nullable
    public QuestionnaireEvidenceViewModel apply(@Nullable QuestionnaireWorkSummary input) {
        if (input == null) {
            throw new NullPointerException("input QuestionnaireWorkSummary must not be null");
        }

        QuestionnaireEvidenceViewModel viewModel = new QuestionnaireEvidenceViewModel();
        viewModel.id = input.getId();
        viewModel.requestedDelete = input.getRequestedDelete() != null;
        viewModel.taskName = input.getTaskName();
        viewModel.serviceRecipientId = input.getServiceRecipientId();
        viewModel.serviceAllocationId = input.getServiceAllocationId();
        viewModel.authorDisplayName = input.getAuthor().getDisplayName();
        viewModel.answers = input.getAnswers().stream().map(answersToSnapshotViewModel).collect(toList());
        viewModel.answers.forEach(a -> a.setWorkDate(input.getWorkDate().toLocalDateTime()));
        viewModel.flags = input.getFlags().stream().map(flagsToViewModel).collect(Collectors.toList());
        viewModel.comment = input.getComment();
        viewModel.minsSpent = input.getCommentMinutesSpent();
        viewModel.workDate = input.getWorkDate().toLocalDateTime(); // TODO: review did involve Locale.ROOT
        viewModel.createdDate = input.getCreatedDate() == null ? null : input.getCreatedDate().toLocalDateTime();
        viewModel.signatureId = input.getSignatureId();

        viewModel.attachments = input.getAttachments() == null ? Collections.emptyList()
                : input.getAttachments().stream()
                .map(f -> fileAssembler.apply(f.getFile()))
                .collect(Collectors.toList());

        return viewModel;
    }

}
