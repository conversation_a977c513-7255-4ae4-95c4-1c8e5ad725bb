package com.ecco.webApi.evidence;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import java.io.IOException;
import javax.annotation.Nonnull;

import com.ecco.dom.EvidenceGroup;
import com.ecco.evidence.EvidenceTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;

@RestController
public class QuestionAnswerCommandController extends BaseWebApiController {

    private final QuestionAnswerCommandHandler questionAnswerCommandHandler;


    @Autowired
    public QuestionAnswerCommandController(
            @Nonnull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            QuestionAnswerCommandHandler questionAnswerCommandHandler) {
        this.commandRepository = serviceRecipientCommandRepository;
        this.questionAnswerCommandHandler = questionAnswerCommandHandler;
    }

    @RequestMapping(
            value = "/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupKey}/{taskName}/questionanswer/{questionDefId}/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result addAnswer(
            QuestionAnswerParams params,
            @Nonnull Authentication authentication,
            @Nonnull @RequestBody String requestBody) throws IOException {

        return questionAnswerCommandHandler.handleCommand(authentication, params, requestBody);
    }

}
