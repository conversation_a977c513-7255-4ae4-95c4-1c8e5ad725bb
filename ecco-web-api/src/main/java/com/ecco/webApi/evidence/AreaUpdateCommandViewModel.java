package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceGroup;
import java.util.UUID;
import javax.annotation.Nullable;

import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import org.springframework.web.util.UriComponentsBuilder;

public class AreaUpdateCommandViewModel extends BaseServiceRecipientCommandViewModel {

    public long areaDefId;
    public UUID workUuid;
    @Nullable
    public ChangeViewModel<Integer> levelChange;
    public ChangeViewModel<String> triggerChange;
    public ChangeViewModel<String> controlChange;

    /** For Jackson etc */
    @Deprecated
    AreaUpdateCommandViewModel() {
        super();
    }

    public AreaUpdateCommandViewModel(UUID workUuid, int serviceRecipientId, EvidenceGroup evidenceGroup,
                                      EvidenceTask evidenceTask, long areaDefId) {
        super(UriComponentsBuilder
                .fromUriString("service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/{sourceTaskName}/area/{areaDefId}/")
                .buildAndExpand(serviceRecipientId, evidenceGroup.nameAsLowercase(), evidenceTask.getTaskName(), areaDefId)
                .toString(),
            serviceRecipientId);
        this.areaDefId = areaDefId;
        this.workUuid = workUuid;
    }

    public boolean hasChanges() {
        return levelChange != null || triggerChange != null || controlChange != null;
    }
}