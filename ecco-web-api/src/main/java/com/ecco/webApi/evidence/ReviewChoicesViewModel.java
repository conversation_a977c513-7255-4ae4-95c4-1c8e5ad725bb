package com.ecco.webApi.evidence;

import org.joda.time.LocalDate;

/**
 * Data-transfer object representing a command to indicate the choices the user has
 * when starting or continuing a review.
 */
public class ReviewChoicesViewModel {

    /**
     * The last incomplete reviewId
     * optional
     * Used to pass through to the evidence screens
     */
    public Long incompleteReviewId;

    /**
     * The page/tab of the current review
     * which is simply the tab-index saved on the review
     * optional
     */
    public Integer reviewPage;

    /**
     * The date the current review was started with
     * optional
     * Used for information purposes only
     */
    public LocalDate currentReviewDate;

    /**
     * The date the last review was started with
     * optional
     * Used for validation purposes only
     */
    public LocalDate lastReviewDate;

    /**
     * The next review date pending
     */
    public LocalDate nextReviewDate;

    /**
     * Threats outstanding - we shouldn't be able to complete reviews with risk outstanding
     */
    public int threatsOutstanding;


    public Long getIncompleteReviewId() {
        return incompleteReviewId;
    }

    public void setIncompleteReviewId(Long incomleteReviewId) {
        this.incompleteReviewId = incomleteReviewId;
    }

    public LocalDate getNextReviewDate() {
        return nextReviewDate;
    }

    public void setNextReviewDate(LocalDate nextReviewDate) {
        this.nextReviewDate = nextReviewDate;
    }

    public int getThreatsOutstanding() {
        return threatsOutstanding;
    }

    public void setThreatsOutstanding(int threatsOutstanding) {
        this.threatsOutstanding = threatsOutstanding;
    }

    public LocalDate getCurrentReviewDate() {
        return currentReviewDate;
    }

    public void setCurrentReviewDate(LocalDate currentReviewDate) {
        this.currentReviewDate = currentReviewDate;
    }
    public void setLastReviewDate(LocalDate lastReviewDate) {
        this.lastReviewDate = lastReviewDate;
    }
    public LocalDate getLastReviewDate() {
        return lastReviewDate;
    }

}
