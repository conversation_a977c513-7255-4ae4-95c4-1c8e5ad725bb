package com.ecco.webApi.evidence;

import org.joda.time.LocalDateTime;

import java.util.UUID;

/**
 * Representation of a flag across a collection of flags spanning work and srIds
 */
public class EvidenceBaseFlagsViewModel {

    public long id;
    public int serviceRecipientId;
    public int serviceAllocationId;
    public int flagId;
    public boolean value;
    public LocalDateTime workDate;
    public UUID workUuid;

}
