package com.ecco.webApi.taskFlow;

import com.ecco.dom.Referral;
import com.ecco.dto.ChangeViewModel;

import javax.annotation.Nonnull;

/**
 * A command which indicates that this serviceRecipientId is to be joined to a parentServiceRecipientId
 * {@link Referral#parentReferral}
 */
public class ReferralTaskJoinCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    static String TASK_JOIN = "join";
    static String TASK_NEWMULTIPLEREFERRAL = "newMultipleReferral"; // the one defined in the db

    @Nonnull
    public ChangeViewModel<Integer> parentServiceRecipientId;

    /** For Jackson etc */
    @Deprecated
    ReferralTaskJoinCommandViewModel() {
        super();
    }

    public ReferralTaskJoinCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_JOIN, taskHandle);
    }

    @Override
    public String toString() {
        return "ReferralTaskJoinCommandViewModel [parentServiceRecipientId=" + parentServiceRecipientId + ", taskName=" + taskName
                + ", serviceRecipientId=" + serviceRecipientId + ", uuid=" + uuid + ", commandUri=" + commandUri + ", timestamp="
                + timestamp + "]";
    }

}
