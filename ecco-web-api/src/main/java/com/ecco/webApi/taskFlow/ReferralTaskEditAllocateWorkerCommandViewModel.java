package com.ecco.webApi.taskFlow;

import com.ecco.dto.ChangeViewModel;

import javax.annotation.Nullable;

public class ReferralTaskEditAllocateWorkerCommandViewModel extends ServiceRecipientTaskCommandViewModel {

    public static String TASK_NAME = "allocateWorker";

    @Nullable
    public ChangeViewModel<Long> allocatedWorkerContactId;


    ReferralTaskEditAllocateWorkerCommandViewModel() {
        super();
    }

    public ReferralTaskEditAllocateWorkerCommandViewModel(int serviceRecipientId, String taskHandle) {
        super(serviceRecipientId, TASK_NAME, taskHandle);
    }

    public ReferralTaskEditAllocateWorkerCommandViewModel(int serviceRecipientId, String taskName, String taskHandle) {
        super(serviceRecipientId, taskName, taskHandle);
    }
}