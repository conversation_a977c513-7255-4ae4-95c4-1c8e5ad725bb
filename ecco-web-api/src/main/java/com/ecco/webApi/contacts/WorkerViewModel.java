package com.ecco.webApi.contacts;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Data-transfer object representing a worker.
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true) // TODO: Remove this.  At least addressedLocationId is provided by client but not handled here
public class WorkerViewModel extends ClientDetailAbstractViewModel {
    /**
     * The worker ID.
     */
    public Long workerId;

    /**
     * The name of the worker as displayed in the user interface.
     */
    public String displayName;

    public Integer primaryLocationId;
    public String primaryLocationName;

    /** aka dbsNumber */
    public String crbNumber;

    public List<WorkerJobViewModel> jobs;
}
