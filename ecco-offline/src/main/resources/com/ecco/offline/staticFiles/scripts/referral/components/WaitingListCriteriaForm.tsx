import * as React from "react"
import {ClassAttributes, FC} from "react";
import {CommandQueue, CommandSource, WaitingListScoreCommand} from "ecco-commands";

import {
    CommandSubform, getGuidanceUrlCallback, LoadingOrError,
    numberInput,
    possiblyModalForm, useServiceRecipientWithEntities,
    useServicesContext, withCommandForm
} from "ecco-components";
import {SessionData, TaskNames} from "ecco-dto";
import {ReferralSummaryDto} from "ecco-dto/referral-dto";
import {CustomForm} from "./CustomForm";

/**
 * TODO shares code with ReferralDetailsForm.tsx
 * TODO also see EditListDefForm for general structure/comments
 */
export const WaitingListCriteriaForm: FC<{serviceRecipientId: number, taskHandle: string, formRef: (c: WaitingListCriteriaSubform) => void}> = props => {

    const {context, error} = useServiceRecipientWithEntities(props.serviceRecipientId);
    const {sessionData} = useServicesContext();
    if (!context || !sessionData) return <LoadingOrError error={error}/>;

    const guidanceFormDefinitionUuid = context.serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(TaskNames.waitingListCriteria, "guidanceFormDefinition");
    const guidanceCallback = guidanceFormDefinitionUuid ? getGuidanceUrlCallback(guidanceFormDefinitionUuid) : undefined;

    return withCommandForm(commandForm =>
        possiblyModalForm(
            "waiting list criteria",
            true, true,
            () => commandForm.cancelForm(),
            () => commandForm.submitForm(),
            false, // TODO could emitChangesTo and see if there are any commands
            false,
            <WaitingListCriteriaSubform
                    ref={props.formRef}
                    sessionData={sessionData}
                    referral={context.referral}
                    taskHandle={props.taskHandle}
                    readOnly={!sessionData.hasRoleReferralEdit()}
                    commandForm={commandForm}
            />,
            undefined,
            guidanceCallback
    ));
}


interface Props extends ClassAttributes<WaitingListCriteriaSubform>{
    readOnly: boolean;
    referral: ReferralSummaryDto;
    taskHandle: string;
    sessionData: SessionData;
}

interface State {
    waitingListScore: number;
}

/**
 * The sub-form rendering.
 * TODO shares code with ReferralDetailsForm.tsx
 */
class WaitingListCriteriaSubform extends CommandSubform<Props, State> implements CommandSource {

    constructor(props) {
        super(props);
        let referral = this.props.referral;
        this.state = {
            waitingListScore: referral.waitingListScore
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        this.queueCommand(commandQueue);
    }

    protected queueCommand(commandQueue: CommandQueue) {
        const cmd = new WaitingListScoreCommand(this.props.referral.serviceRecipientId, this.props.taskHandle)
            .changeScore(this.props.referral.waitingListScore, this.state.waitingListScore);
        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    render() {
        const setter = state => this.setState(state);

        const WaitingListDetails = <div className="container-fluid v-gap-15">
                {numberInput("waitingListScore", "waiting list score", setter, this.state)}
            </div>;

        const WaitingListForm: React.ReactElement =
            <div className="v-gap-15">
                <CustomForm serviceRecipientId={this.props.referral.serviceRecipientId}
                            taskName={TaskNames.waitingListCriteria}
                            taskNameGroup={TaskNames.waitingListCriteria}
                            page={'embedded'}
                            taskHandle={this.props.taskHandle} readOnly={this.props.readOnly}/>
            </div>

        return (
            <>
                {WaitingListDetails}
                {WaitingListForm}
            </>
    );

        /* NOTE: the order of this in the JSX is important.
         * See https://facebook.github.io/react/docs/transferring-props.html */
    }

}
