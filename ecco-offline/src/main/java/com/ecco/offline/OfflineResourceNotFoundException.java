package com.ecco.offline;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class OfflineResourceNotFoundException extends Exception {
    private static final long serialVersionUID = -1503797037923975677L;

    private String path;

    public OfflineResourceNotFoundException(String path) {
        super("Offline resource not found: " + path);
        this.path = path;
    }

    public String getPath() {
        return path;
    }
}
