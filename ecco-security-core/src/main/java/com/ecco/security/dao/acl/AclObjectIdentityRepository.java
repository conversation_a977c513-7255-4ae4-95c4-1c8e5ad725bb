package com.ecco.security.dao.acl;

import com.ecco.security.acl.dom.AclObjectIdentity;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;

import java.util.Collection;

public interface AclObjectIdentityRepository extends CrudRepository<AclObjectIdentity, Long>, JpaSpecificationExecutor<AclObjectIdentity> {
    Iterable<AclObjectIdentity> findByParentObject_ObjectIdClass_ClassNameAndParentObject_ObjectIdIdentity(String className, Long objectIdIdentity);

    Iterable<AclObjectIdentity> findByIdInOrderByObjectIdIdentityAsc(Collection<Long> ids);

    AclObjectIdentity findByObjectIdClass_ClassNameAndObjectIdIdentity(String className, Long objectIdIdentity);
}
